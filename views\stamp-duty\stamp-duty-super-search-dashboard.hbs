<main class="">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <div class="card-box">
          <div class="card-title">
            <h2>The Collector of Stamp Duty (Super Search)</h2>
          </div>
          <div class="row mt-3">
            <div class="col-12 ml-1">
              <h4>Search filters:</h4>
            </div>
          </div>
          <div class="row p-1">
            <div class="col-md-11">
              <form method="POST" id="searchForm">
                <div class="row my-2">
                  <div class="col-md-4">
                    <label for="exemptionType">Exemption type</label>
                    <select name="exemptionType" id='exemptionType' class="form-control custom-select" onchange="">
                      <option value="" selected>Select...</option>
                      <option id="all" value="all" {{#ifEquals filters.exemptionType 'all' }} selected {{/ifEquals}}>All
                      </option>
                      <option id="love-and-affection" value="love-and-affection" {{#ifEquals
                        filters.exemptionType 'love-and-affection' }} selected {{/ifEquals}}>
                        Love and affection
                      </option>
                      <option id="first-time-buyer" value="first-time-buyer" {{#ifEquals
                        filters.exemptionType 'first-time-buyer' }} selected {{/ifEquals}}>
                        First time buyer
                      </option>
                      <option id="remissions" value="remissions" {{#ifEquals filters.exemptionType 'remissions' }}
                        selected {{/ifEquals}}>
                        Remissions
                      </option>
                      <option id="exemption-application" value="exemption-application" {{#ifEquals
                        filters.exemptionType 'exemption-application' }} selected {{/ifEquals}}>
                        Stamp Duty Exemption
                      </option>
                      <option id="import-duty-waiver" value="Import Duty Waiver" {{#ifEquals
                        filters.exemptionType 'Import Duty Waiver' }} selected {{/ifEquals}}>
                        Import Duty Waiver
                      </option>
                      <option id="stamp-duty-reduction" value="Stamp Duty Reduction" {{#ifEquals
                        filters.exemptionType 'Stamp Duty Reduction' }} selected {{/ifEquals}}>
                        Stamp Duty Reduction
                      </option>
                    </select>
                  </div>

                  <div class="col-md-4">
                    <label for="auditReady">Audit ready</label>
                    <select name="auditReady" id='auditReady' class="form-control custom-select" onchange="">
                      <option value="" selected>Select...</option>
                      <option id="yes" value="Yes" {{#ifEquals filters.auditReady 'Yes' }} selected {{/ifEquals}}>Yes
                      </option>
                      <option id="no" value="No" {{#ifEquals filters.auditReady 'No' }} selected {{/ifEquals}}>No
                      </option>
                    </select>
                  </div>

                  <div class="col-md-4">
                    <input class='form-control' type='text' name='searchFilter'
                      placeholder="Enter at least 3 characters" id='search_filter' value="{{filters.searchFilter}}"
                      onkeypress="return (event.charCode > 64 && event.charCode < 91) || (event.charCode > 96 && event.charCode < 123) || event.charCode ==32 || (event.charCode > 47 && event.charCode < 58)" />
                    <label for="search_filter"></label>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-8">
                    <label for="submittedStart">Submitted</label>
                    <div class="input-group mb-3">
                      <input type="date" name="submittedStart" id="submittedStart" class="form-control"
                        value="{{filters.submittedStart}}">
                      <div class="input-group-append">
                        <span class="input-group-text">to</span>
                      </div>
                      <input type="date" name="submittedEnd" id="submittedEnd" class="form-control"
                        value="{{filters.submittedEnd}}">
                    </div>
                  </div>
                  <div class="col-md-4">
                    <button type="submit" class='btn btn-light btn-sm waves-effect'>Search</button>
                    <input type="button" class='btn btn-light btn-sm' value="Clear"
                      onclick="$('#searchForm .form-control').val('')"></input>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-check">
                      <input type="checkbox" class="form-check-input" id="showPendingActions" name="showPendingActions"
                        value="true" {{#if filters.showPendingActions}}checked{{/if}}>
                      <label class="form-check-label" for="showPendingActions">
                        Show only pending actions
                      </label>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
          <div class="row">
            <div class="col-12">
              <h4>Results</h4>
            </div>
          </div>
          <div class="row">
            <div class="col-12">
              <div class="table-responsive">
                <table id="scroll-horizontal-datatable" class="table table-striped w-100 nowrap">
                  <thead>
                    <tr>
                      <th class="tableYellow">Actions</th>
                      <th class="tableYellow">Status</th>
                      <th class="tableYellow">Id</th>
                      <th class="tableYellow">Timestamp</th>
                      <th class="tableYellow">Date submitted</th>
                      <th class="tableYellow">Reference Nr</th>
                      <th class="tableYellow">Applicant</th>
                      <th class="tableYellow">Transferee</th>
                      <th class="tableYellow">Parcel #</th>
                      <th class="tableYellow">District</th>
                      <th class="tableYellow">Island</th>
                      <th class="tableYellow">Remitted Amount</th>
                      <th class="tableYellow">Exemption type</th>
                    </tr>
                  </thead>
                  <tbody>
                    {{#each allSubmissions}}
                    <tr class="{{rowClass}}">
                      <td>{{#if hasActionsPending}}<i class="fas fa-exclamation-triangle text-danger"></i>{{/if}}</td>
                      <td>{{ status }}</td>
                      <td>{{ _id }}</td>
                      <td>{{formatDate createdAt "x"}}</td>
                      <td>{{formatDate createdAt "DD/MM/YYYY"}}</td>
                      <td>{{ referenceNr }}</td>
                      {{#ifEquals type 'exemption'}}
                      <td>{{ transferorName }}</td>
                      <td>{{ transfereeName }}</td>
                      <td>{{ parcelNumber }}</td>
                      <td>{{ district }}</td>
                      <td>{{ island }}</td>
                      <td>$ {{ value }}</td>
                      <td>{{ exemptionType }}</td>
                      {{else ifEquals type 'stampDutyApplication'}}
                      <td>{{ firstName }} {{lastName}}</td>
                      <td></td>
                      <td>{{ propertyDetails.parcel }}</td>
                      <td>{{ propertyDetails.district }}</td>
                      <td>{{ island }}</td>
                      <td>$ {{ formatNumber remittedAmount }}</td>
                      <td>Stamp Duty Exemption</td>
                      {{else ifEquals type 'importDutyWaiver'}}
                      <td>{{ firstName }} {{lastName}}</td>
                      <td></td>
                      <td>{{ propertyDetails.parcel }}</td>
                      <td>{{ propertyDetails.district }}</td>
                      <td>{{ propertyDetails.island }}</td>
                      <td>$ {{formatNumber remittedAmount }}</td>
                      <td>Import Duty Waiver</td>
                      {{else ifEquals type 'reductionApplication'}}
                      <td>{{ firstName }} {{lastName}}</td>
                      <td></td>
                      <td>{{ propertyDetails.parcel }}</td>
                      <td>{{ propertyDetails.district }}</td>
                      <td>{{ propertyDetails.island }}</td>
                      <td>$ {{formatNumber remittedAmount }}</td>
                      <td>Stamp Duty Reduction</td>
                      {{/ifEquals}}
                    </tr>
                    {{/each}}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <!-- CONTENT END -->
          <div class="row mt-2 justify-content-between ">
            <a href="/stamp-duty/dashboard" class="btn btn-secondary width-lg waves-effect waves-light">
              Back
            </a>

            <button class="btn solid btn-warning" id="btn-export-xls">
              Export

            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>
<script type="text/javascript" src="/javascripts/export-xls.js"></script>
<script type="text/javascript">
  let table;
  let currentSelectedApplicationId;
  let currentSelectedApplicationType;
  $(document).ready(function () {
    table = $("#scroll-horizontal-datatable").DataTable({
      "columnDefs": [
        { "visible": false, "targets": [2, 3] }, // Hide ID and Timestamp columns
        { "orderable": false, "targets": [0] }, // Actions column not orderable (sorted from backend)
        { "type": "date", "targets": [4] } // Date column for proper sorting
      ],
      "order": [], // No default ordering since data comes pre-sorted from backend
      "ordering": false, // Disable ordering since data is pre-sorted from backend
      scrollX: !0,
      select: { style: "single" },
      language: {
        paginate: {
          previous: "<i class='mdi mdi-chevron-left'>",
          next: "<i class='mdi mdi-chevron-right'>"
        }
      },
      drawCallback: function () {
        $(".dataTables_paginate > .pagination").addClass("pagination-rounded")
      }
    });
    table.on('select', function (e, dt, type, indexes) {
      if (type === 'row') {
        let selectedRowData = table.rows(indexes).data();
        currentSelectedApplicationId = selectedRowData[0][2]; // ID is now in column 2

        // Determine the route based on the row class
        let rowClass = $(table.rows(indexes).nodes()[0]).attr('class');
        if (rowClass.includes('exemptions-row')) {
          window.location.href = "/stamp-duty/" + currentSelectedApplicationId + "/open";
        } else if (rowClass.includes('stamp-duty-exemption-row')) {
          window.location.href = "/stamp-duty/exemption-application/" + currentSelectedApplicationId + "/update";
        } else if (rowClass.includes('duty-waiver-row')) {
          window.location.href = "/stamp-duty/import-duty-waiver/" + currentSelectedApplicationId + "/open";
        } else if (rowClass.includes('reduction-application-row')) {
          window.location.href = "/stamp-duty/reduction-application/" + currentSelectedApplicationId + "/update";
        }
      }
    });
  });

  $('#btn-export-xls').click(function () {
    exportXlsFile("stampDutyOfficer");
  });

  // Auto-submit form when "Show only pending actions" checkbox changes
  $('#showPendingActions').change(function () {
    $('#searchForm').submit();
  });
</script>