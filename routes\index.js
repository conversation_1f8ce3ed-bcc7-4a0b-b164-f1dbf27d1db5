const express = require('express');
const router = express.Router();
const LandOfficerModel = require('../models/exemptions');

/* GET home page. */
router.get('/', ensureAuthenticated, async function (req, res) {
    let submitted;
    let saved;
    let requested;
    let pendingPS;
    let declined;
    let approvedPS;
    let transferPending;
    let transferCompleted;
    let completed;
    let countDocuments;
    let naturalLove;
    let sectionExemption;
    let charitableInstitutions;
    let homeOwnerPolicy;
    let countByType;
    let conflict;

    countDocuments = await LandOfficerModel.aggregate([
        {
            $match: {
                "status": {
                    $in: ['SUBMITTED', 'SAVED', 'REQUESTED', 'PENDING PS', 'DECLINED', 'APPROVED PS', 'TRANSFER PENDING', 'TRANSFER COMPLETED', 'COMPLETED', 'CONFLICT']
                }
            },
        },
        {$group: {_id: "$status", count: {$sum: 1}}}
    ]);
    if (countDocuments) {
        submitted = countDocuments.find((document) => document._id === "SUBMITTED");
        saved = countDocuments.find((document) => document._id === "SAVED");
        requested = countDocuments.find((document) => document._id === "REQUESTED");
        pendingPS = countDocuments.find((document) => document._id === "PENDING PS");
        declined = countDocuments.find((document) => document._id === "DECLINED");
        approvedPS = countDocuments.find((document) => document._id === "APPROVED PS");
        transferPending = countDocuments.find((document) => document._id === "TRANSFER PENDING");
        transferCompleted = countDocuments.find((document) => document._id === "TRANSFER COMPLETED");
        completed = countDocuments.find((document) => document._id === "COMPLETED");
        conflict = countDocuments.find((document) => document._id === "CONFLICT");
    }

    countByType = await LandOfficerModel.aggregate([
        {
            $match: {
                "exemptionType": {
                    $in: ['Natural Love & Affection', 'Section 23- Companies & 28- Bodies Corporate', 'Transfers to Charitable Institutions', 'Home Owner Policy']
                }
            },
        },
        {$group: {_id: "$exemptionType", count: {$sum: 1}}}
    ]);
    if (countByType) {
        naturalLove = countByType.find((documentType) => documentType._id === "Natural Love & Affection");
        sectionExemption = countByType.find((documentType) => documentType._id === "Section 23- Companies & 28- Bodies Corporate");
        charitableInstitutions = countByType.find((documentType) => documentType._id === "Transfers to Charitable Institutions");
        homeOwnerPolicy = countByType.find((documentType) => documentType._id === "Home Owner Policy");
    }

    res.render('index',
        {
            user: req.session.user,
            authentication: req.session.authentication,
            submitted: submitted ? submitted.count : 0,
            saved: saved ? saved.count : 0,
            requested: requested ? requested.count : 0,
            pendingPS: pendingPS ? pendingPS.count : 0,
            declined: declined ? declined.count : 0,
            approvedPS: approvedPS ? approvedPS.count : 0,
            transferPending: transferPending ? transferPending.count : 0,
            transferCompleted: transferCompleted ? transferCompleted.count : 0,
            completed: completed ? completed.count : 0,
            conflict: conflict ? conflict.count : 0,
            naturalLove: naturalLove ? naturalLove.count : 0,
            sectionExemption: sectionExemption ? sectionExemption.count : 0,
            charitableInstitutions: charitableInstitutions ? charitableInstitutions.count : 0,
            homeOwnerPolicy: homeOwnerPolicy ? homeOwnerPolicy.count : 0,
        });
});

function ensureAuthenticated(req, res, next) {
    if (req.session && req.session.authentication && req.session.user) {
        if (req.session.authentication.isStampDuty || req.session.authentication.isLandOfficer ||
            req.session.authentication.isPsOfficer || req.session.authentication.isFinance || req.session.authentication.isAuditor ||
            req.session.authentication.isCustoms || req.session.authentication.isDeputyCommissioner) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        res.redirect('/login');
    }
}

module.exports = router;
